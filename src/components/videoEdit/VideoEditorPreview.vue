<template>
  <div class="video-editor-preview">
    <div class="preview-wrapper" ref="previewWrapper">
      <div class="preview-container" :style="containerStyle">
        <!-- 视频预览 currentShot?.type 为null 则背景透明 :style="{ backgroundColor: currentShot?.type == 'video' || currentShot?.type == 'image' ? 'black' : 'transparent' }" -->
        <div class="video-container" :style="{backgroundColor: isPlaying ? '#000':'transparent'}" >

          <!-- 视频播放  && isPlaying -->
          <video v-if="currentShot?.type == 'video'" ref="videoPlayer" class="video-player" @ended="onVideoEnded"
            @loadstart="() => console.log('视频开始加载:', currentShot.videoUrl)"
            @loadeddata="() => console.log('视频数据加载完成:', currentShot.videoUrl)"
            @canplay="() => console.log('视频可以播放:', currentShot.videoUrl)"
            @error="(e) => console.error('视频播放错误:', e, currentShot.videoUrl)"
            :src="currentShot.videoUrl" :style="{ objectFit: currentShot.displayType || 'cover' }"
            preload="auto" playsInline>
            <source :src="currentShot.videoUrl" type="video/mp4">
            您的浏览器不支持视频播放
          </video>

          <!-- 图片预览 -->
          <div v-else-if="currentShot?.type == 'image'" class="image-preview">
            <img :src="currentShot.imageUrl" alt="预览图" class="preview-image"
              :style="{ ...imageAnimationStyle, objectFit: currentShot.displayType || 'cover' }" />
          </div>

          <div v-else class="empty-preview">
            <!-- <el-icon><VideoCamera /></el-icon> -->
            <p>请设置素材</p>
          </div>

          <!-- 下一个分镜预加载层 -->
          <div v-if="nextShot && showNextShotPreview" class="next-shot-preview"
            :style="{ opacity: preloadNextShotOpacity }">
            <!-- 预加载视频首帧 -->
            <video v-if="nextShot.type === 'video'" ref="nextVideoPreview" class="next-video-preview"
              :src="nextShot.videoUrl" :style="{ objectFit: nextShot.displayType || 'cover' }" preload="auto">
              <source :src="nextShot.videoUrl" type="video/mp4">
            </video>

            <!-- 预加载图片 -->
            <img v-else-if="nextShot.type === 'image'" :src="nextShot.imageUrl" alt="下一分镜预览" class="next-image-preview"
              :style="{ objectFit: nextShot.displayType || 'cover' }" />
          </div>
        </div>

        <!-- 播放控制 -->
        <div class="controls-overlay" v-if="currentShot && false" @click="togglePlayPause">
          <div class="play-pause-icon" v-if="!isPlaying">
            <div class="play-icon">▶</div>
          </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar" v-if="currentShot && false">
          <div class="progress-track">
            <div class="progress-fill" :style="{ width: `${progressPercentage}%` }"></div>
          </div>
          <div class="time-display">
            {{ formatTime(currentTime) }} / {{ formatTime(totalDuration) }}
          </div>
        </div>

        <!-- 分镜导航 -->
        <div class="shot-navigation" v-if="shots && shots.length > 1 && false">
          <button class="nav-button prev" @click.stop="playPreviousShot" :disabled="currentShotIndex <= 0">
            <div class="nav-icon">◀</div>
          </button>
          <div class="shot-indicator">{{ currentShotIndex + 1 }} / {{ shots.length }}</div>
          <button class="nav-button next" @click.stop="playNextShot" :disabled="currentShotIndex >= shots.length - 1">
            <div class="nav-icon">▶</div>
          </button>
        </div>

        <!-- 字幕显示 -->
        <div class="subtitle-container" v-if="showSubtitle">
          <div class="subtitle-text" :style="{ fontSize: subtitleFontSize }">{{ currentSubtitle }}</div>
        </div>

        <!-- 资源加载状态指示器 -->
        <div class="resource-loading-indicator" v-if="isPreloading && !preloadCompleted">
          <div class="loading-spinner"></div>
          <div class="loading-text">正在预加载资源...</div>
        </div>

        <div v-if="currentShot && currentShot.shotStatus == '1'" class="loading-content">
          <div class="loading-overlay">
            <div class="loading-icon">
              <svg class="circular-icon" viewBox="0 0 50 50">
                <circle class="path" cx="25" cy="25" r="20" fill="none" />
              </svg>
            </div>
            <div class="loading-text">生成中...</div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, computed } from 'vue';
import { videoPlaybackDebugger } from '@/utils/videoPlaybackDebug.js';

// 组件属性
const props = defineProps({
  shots: {
    type: Array,
    default: () => []
  },
  startIndex: {
    type: Number,
    default: 0
  },
  defaultAspectRatio: {
    type: String,
    default: '16:9'
  },
  autoPlay: {
    type: Boolean,
    default: false
  },
  showSubtitles: {
    type: Boolean,
    default: true
  }
});

// 事件
const emit = defineEmits([
  'time-update',
  'shot-change',
  'play-state-change',
  'playback-completed'
]);

// 状态变量
const videoPlayer = ref(null);
const audioPlayer = ref(null);
const previewWrapper = ref(null);
const wrapperSize = ref({ width: 0, height: 0 });
const isPlaying = ref(false);
const currentTime = ref(0);
const totalDuration = ref(0);
const aspectRatio = ref(props.defaultAspectRatio);
const currentShotIndex = ref(props.startIndex);
const imageAnimationProgress = ref(0);
const imageAnimationInterval = ref(null);
const currentSubtitle = ref(''); // 当前显示的字幕文本
const videoAnimationFrameId = ref(null); // 存储requestAnimationFrame的ID
const isVideoEnded = ref(false); // 视频是否已结束但分镜仍在播放（等待音频完成）
const shotStartTimestamp = ref(0); // 记录当前分镜开始播放的时间戳
const nextVideoPreview = ref(null); // 下一个视频预览元素
const nextVideoLoaded = ref(false); // 下一个视频是否已加载首帧
const isFullscreen = ref(false); // 全屏状态

// 资源缓存相关
const videoCache = ref({}); // 视频资源缓存 {url: HTMLVideoElement}
const imageCache = ref({}); // 图片资源缓存 {url: HTMLImageElement}
const audioCache = ref({}); // 音频资源缓存 {url: HTMLAudioElement}
const resourceLoadingStatus = ref({}); // 资源加载状态 {url: 'loading'|'loaded'|'error'}
const preloadQueue = ref([]); // 预加载队列
const isPreloading = ref(false); // 是否正在预加载
const preloadCompleted = ref(false); // 是否完成预加载

const defaultDuration = 1000; // 默认时长5秒

// 计算属性
const currentShot = computed(() => {
  return props.shots[currentShotIndex.value] || null;
});

// 获取下一个分镜
const nextShot = computed(() => {
  // 如果当前是最后一个分镜，则返回null
  if (currentShotIndex.value >= props.shots.length - 1) {
    return null;
  }
  // 否则返回下一个分镜
  return props.shots[currentShotIndex.value + 1] || null;
});

// 是否显示下一个分镜预加载
const showNextShotPreview = computed(() => {
  // 如果没有下一个分镜，不显示
  if (!nextShot.value) return false;

  // 如果没有在播放，不显示
  if (!isPlaying.value) return false;

  // 获取当前分镜的最大时长（毫秒）
  const maxDuration = getCurrentShotMaxDuration() / 1000; // 转换为秒

  // 计算当前分镜已经播放的时间
  const previousDuration = calculatePreviousShotsDuration();
  const currentPlayTime = currentTime.value - previousDuration;

  // 当播放进度超过75%时显示预加载层
  return currentPlayTime / maxDuration > 0.75;
});

// 预加载层透明度
const preloadNextShotOpacity = computed(() => {
  // 如果是视频，显示预加载层
  // if(currentShot.value?.type != 'video'){
  //   return 0;
  // }
  return 0;

  // 获取当前分镜的最大时长（秒）
  const maxDuration = getCurrentShotMaxDuration() / 1000;

  // 计算当前分镜已经播放的时间
  const previousDuration = calculatePreviousShotsDuration();
  const currentPlayTime = currentTime.value - previousDuration;

  // 计算播放进度百分比
  const playProgress = currentPlayTime / maxDuration;

  // 从75%到100%，透明度从0逐渐增加到0.3
  if (playProgress >= 0.75 && playProgress <= 1) {
    // 将0.75-1的范围映射到0-1的范围
    const normalizedProgress = (playProgress - 0.75) * 4; // 0.75->0, 1->1
    // 透明度最大为0.3
    return normalizedProgress * 0.3;
  }

  return 0;
});

// 字幕显示控制
const showSubtitle = computed(() => {
  return props.showSubtitles && currentSubtitle.value && currentSubtitle.value.trim() !== '';
});

// 计算总时长
const calculateTotalDuration = () => {
  return props.shots.reduce((total, shot) => {
    // 计算所有语音的总时长
    let countVoiceDuration = 0;
    if (shot.audios && shot.audios.length > 0) {
      countVoiceDuration = shot.audios.reduce((acc, voice) => acc + (voice.audioDuration || 0), 0);
    }else if(shot?.videoDuration > 0){
      countVoiceDuration = shot.videoDuration;
    }

    let duration;
    // 如果视频时长大于0且大于语音总时长，返回视频时长
    // if (shot.videoDuration > 0 && shot.videoDuration > countVoiceDuration) {
    //   duration = shot.videoDuration;
    // } else 
    if (shot.playDuration && shot.playDuration > 0 && shot.playDuration > countVoiceDuration) {
      duration = shot.playDuration;
    }else if (countVoiceDuration > 0) {
      // 如果有语音总时长，返回语音总时长
      duration = countVoiceDuration;
    } else {
      // 最后使用默认时长
      duration = defaultDuration; // 默认5秒
    }

    return total + duration;
  }, 0);
};

// 资源预加载相关方法
// 初始化预加载队列
const initPreloadQueue = () => {
  preloadQueue.value = [];

  // 首先添加当前分镜和下一个分镜到队列
  if (currentShot.value) {
    addShotToPreloadQueue(currentShot.value, true);
  }

  if (nextShot.value) {
    addShotToPreloadQueue(nextShot.value, true);
  }

  // 然后添加其他分镜到队列
  props.shots.forEach((shot, index) => {
    if (index !== currentShotIndex.value && index !== currentShotIndex.value + 1) {
      addShotToPreloadQueue(shot);
    }
  });

  // 开始预加载
  startPreloading();
};

// 将分镜添加到预加载队列
const addShotToPreloadQueue = (shot, isPriority = false) => {
  if (!shot) return;

  // 添加视频资源
  if (shot.type === 'video' && shot.videoUrl) {
    addToPreloadQueue(shot.videoUrl, 'video', isPriority);
  }

  // 添加图片资源
  if (shot.type === 'image' && shot.imageUrl) {
    addToPreloadQueue(shot.imageUrl, 'image', isPriority);
  }

  // 添加音频资源
  if (shot.audios && shot.audios.length > 0) {
    shot.audios.forEach(audio => {
      if (audio.audioUrl) {
        addToPreloadQueue(audio.audioUrl, 'audio', isPriority);
      }
    });
  }
};

// 添加资源到预加载队列
const addToPreloadQueue = (url, type, isPriority = false) => {
  // 如果URL为空，跳过
  if (!url) return;

  // 检查是否已经在队列中
  const isInQueue = preloadQueue.value.some(item => item.url === url);

  // 如果资源已经在缓存中、正在加载或已在队列中，则跳过
  if (resourceLoadingStatus.value[url] === 'loaded' ||
    resourceLoadingStatus.value[url] === 'loading' ||
    isInQueue) {
    // 如果已在队列中但需要提高优先级，则移除旧的并重新添加
    if (isInQueue && isPriority) {
      preloadQueue.value = preloadQueue.value.filter(item => item.url !== url);
      preloadQueue.value.unshift({ url, type });
      console.log(`资源已在队列中，提高优先级: ${url}`);
    }
    return;
  }

  // 标记资源为正在加载
  resourceLoadingStatus.value[url] = 'loading';

  // 添加到队列
  if (isPriority) {
    // 优先级高的资源添加到队列前面
    preloadQueue.value.unshift({ url, type });
    console.log(`添加高优先级资源到队列: ${url}`);
  } else {
    preloadQueue.value.push({ url, type });
    console.log(`添加资源到队列: ${url}`);
  }
};

// 开始预加载资源
const startPreloading = async () => {
  if (isPreloading.value || preloadQueue.value.length === 0) {
    if (preloadQueue.value.length === 0) {
      preloadCompleted.value = true;
    }
    return;
  }

  isPreloading.value = true;

  // 每次只加载一个资源，避免并发导致的资源竞争
  const item = preloadQueue.value.shift();

  if (item) {
    console.log(`开始预加载资源: ${item.url} (类型: ${item.type})`);

    try {
      await preloadResource(item.url, item.type);
      console.log(`资源预加载成功: ${item.url}`);
    } catch (error) {
      console.error(`资源预加载失败: ${item.url}`, error);
    }
  }

  isPreloading.value = false;

  // 继续预加载剩余资源
  if (preloadQueue.value.length > 0) {
    // 使用setTimeout避免调用栈过深
    setTimeout(() => {
      startPreloading();
    }, 0);
  } else {
    preloadCompleted.value = true;
    console.log('所有资源预加载完成');
  }
};

// 预加载单个资源
const preloadResource = async (url, type) => {
  try {
    if (type === 'video') {
      await preloadVideo(url);
    } else if (type === 'image') {
      await preloadImage(url);
    } else if (type === 'audio') {
      await preloadAudio(url);
    }

    resourceLoadingStatus.value[url] = 'loaded';
  } catch (error) {
    console.error(`预加载资源失败: ${url}`, error);
    resourceLoadingStatus.value[url] = 'error';
  }
};

// 预加载视频
const preloadVideo = (url) => {
  return new Promise((resolve, reject) => {
    console.log(`开始预加载视频: ${url}`);

    // 如果已经在缓存中，直接返回
    if (videoCache.value[url]) {
      console.log(`视频已在缓存中: ${url}`);
      resolve();
      return;
    }

    const video = document.createElement('video');
    video.preload = 'auto';
    video.muted = false;
    video.playsInline = true;
    video.crossOrigin = 'anonymous'; // 添加跨域支持

    // 设置超时处理
    const timeout = setTimeout(() => {
      console.error(`视频加载超时: ${url}`);
      reject(new Error(`视频加载超时: ${url}`));
    }, 30000); // 30秒超时

    // 设置事件监听器
    video.onloadeddata = () => {
      // 视频加载完成后，设置到第一帧并暂停
      clearTimeout(timeout);
      video.currentTime = 0;
      video.pause();
      videoCache.value[url] = video;
      console.log(`视频预加载完成: ${url}`);
      resolve();
    };

    video.oncanplaythrough = () => {
      // 视频可以完整播放时也认为加载完成
      if (!videoCache.value[url]) {
        clearTimeout(timeout);
        video.currentTime = 0;
        video.pause();
        videoCache.value[url] = video;
        console.log(`视频预加载完成(canplaythrough): ${url}`);
        resolve();
      }
    };

    video.onerror = (error) => {
      clearTimeout(timeout);
      console.error(`视频加载失败: ${url}`, error);
      // 标记为错误状态
      resourceLoadingStatus.value[url] = 'error';
      reject(error);
    };

    video.onabort = () => {
      clearTimeout(timeout);
      console.warn(`视频加载被中断: ${url}`);
      reject(new Error(`视频加载被中断: ${url}`));
    };

    // 开始加载
    video.src = url;
    video.load();
  });
};

// 预加载图片
const preloadImage = (url) => {
  return new Promise((resolve, reject) => {
    // 如果已经在缓存中，直接返回
    if (imageCache.value[url]) {
      resolve();
      return;
    }

    const img = new Image();

    img.onload = () => {
      imageCache.value[url] = img;
      resolve();
    };

    img.onerror = (error) => {
      console.error(`图片加载失败: ${url}`, error);
      reject(error);
    };

    // 开始加载
    img.src = url;
  });
};

// 预加载音频
const preloadAudio = (url) => {
  return new Promise((resolve, reject) => {
    // 如果已经在缓存中，直接返回
    if (audioCache.value[url]) {
      console.log(`音频已在缓存中，跳过预加载: ${url}`);
      resolve();
      return;
    }

    console.log(`开始预加载音频: ${url}`);
    const audio = new Audio();

    // 设置超时处理
    const timeout = setTimeout(() => {
      console.warn(`音频加载超时: ${url}`);
      reject(new Error(`音频加载超时: ${url}`));
    }, 30000); // 30秒超时

    audio.onloadeddata = () => {
      clearTimeout(timeout);
      console.log(`音频加载完成: ${url}`);
      audioCache.value[url] = audio;
      resolve();
    };

    audio.onerror = (error) => {
      clearTimeout(timeout);
      console.error(`音频加载失败: ${url}`, error);
      reject(error);
    };

    // 开始加载
    audio.preload = 'auto';
    audio.src = url;
  });
};

// 从缓存中获取资源
const getResourceFromCache = (url, type) => {
  if (type === 'video') {
    return videoCache.value[url];
  } else if (type === 'image') {
    return imageCache.value[url];
  } else if (type === 'audio') {
    return audioCache.value[url];
  }
  return null;
};

// 清理不需要的资源缓存
const cleanupCache = () => {
  // 获取当前所有需要的资源URL
  const neededUrls = new Set();

  props.shots.forEach(shot => {
    if (shot.type === 'video' && shot.videoUrl) {
      neededUrls.add(shot.videoUrl);
    } else if (shot.type === 'image' && shot.imageUrl) {
      neededUrls.add(shot.imageUrl);
    }

    if (shot.audios && shot.audios.length > 0) {
      shot.audios.forEach(audio => {
        if (audio.audioUrl) {
          neededUrls.add(audio.audioUrl);
        }
      });
    }
  });

  // 清理视频缓存
  Object.keys(videoCache.value).forEach(url => {
    if (!neededUrls.has(url)) {
      delete videoCache.value[url];
      delete resourceLoadingStatus.value[url];
    }
  });

  // 清理图片缓存
  Object.keys(imageCache.value).forEach(url => {
    if (!neededUrls.has(url)) {
      delete imageCache.value[url];
      delete resourceLoadingStatus.value[url];
    }
  });

  // 清理音频缓存
  Object.keys(audioCache.value).forEach(url => {
    if (!neededUrls.has(url)) {
      delete audioCache.value[url];
      delete resourceLoadingStatus.value[url];
    }
  });
};

// 监听shots变化重新计算总时长
watch(() => props.shots, () => {
  totalDuration.value = calculateTotalDuration() / 1000; // 转换为秒

  // 当分镜列表变化时，重新初始化预加载队列
  if (props.shots && props.shots.length > 0) {
    console.log('分镜列表变化，重新初始化预加载队列');
    // 清理不需要的缓存资源
    cleanupCache();
    // 重新初始化预加载队列
    initPreloadQueue();
  }
}, { deep: true, immediate: true });

// 计算容器样式
const containerStyle = computed(() => {
  // 解析宽高比
  const parts = aspectRatio.value.split(':');
  if (parts.length !== 2) return {};

  const width = parseInt(parts[0]);
  const height = parseInt(parts[1]);

  // 获取容器尺寸
  const containerWidth = wrapperSize.value.width - 12; // 减去 margin
  const containerHeight = wrapperSize.value.height - 12; // 减去 margin

  // 计算基于宽度和高度的尺寸
  const widthBasedHeight = containerWidth * (height / width);
  const heightBasedWidth = containerHeight * (width / height);

  // 计算缩放比例和位置
  let scale = 1;
  let finalWidth, finalHeight;

  // 统一使用一种布局策略，根据容器比例决定缩放方式
  if (widthBasedHeight > containerHeight) {
    // 高度受限，按高度缩放
    scale = containerHeight / widthBasedHeight;
    finalWidth = containerWidth * scale;
    finalHeight = containerHeight;
  } else {
    // 宽度受限，按宽度缩放
    finalWidth = containerWidth;
    finalHeight = widthBasedHeight;
  }

  // 统一返回固定尺寸的容器，避免在模式切换时使用不同的布局策略
  return {
    position: 'relative',
    width: `${finalWidth}px`,
    height: `${finalHeight}px`,
    // margin: '6px auto',
    overflow: 'hidden',
    // 使用transform确保平滑过渡
    transform: `translateZ(0)`,
    transformOrigin: 'center center',
    // 添加原始数值，方便其他计算属性使用
    finalWidth,
    finalHeight
  };
});

// 图片动画样式
const imageAnimationStyle = computed(() => {
  if (!currentShot.value || !currentShot.value.movement) return {
    transform: 'translateZ(0)', // 默认启用硬件加速
    willChange: 'transform'
  };

  const progress = imageAnimationProgress.value;
  const baseStyle = {
    willChange: 'transform',
    backfaceVisibility: 'hidden',
    transition: 'transform 0.02s linear'
  };

  // 根据movement类型应用不同的动画效果
  switch (currentShot.value.movement) {
    case 'zoom-in':
      return {
        ...baseStyle,
        transform: `translateZ(0) scale(${1 + progress * 0.25})`
      };
    case 'zoom-out':
      return {
        ...baseStyle,
        transform: `translateZ(0) scale(${1.25 - progress * 0.25})`
      };
    case 'pan-right':
      return {
        ...baseStyle,
        transform: `translateZ(0) scale(${1 + progress * 0.15}) translateX(${-35 * progress}px)`
      };
    case 'pan-left':
      return {
        ...baseStyle,
        transform: `translateZ(0) scale(${1 + progress * 0.15}) translateX(${35 * progress}px)`
      };
    case 'tilt-up':
      return {
        ...baseStyle,
        transform: `translateZ(0) scale(${1 + progress * 0.15}) translateY(${35 * progress}px)`
      };
    case 'tilt-down':
      return {
        ...baseStyle,
        transform: `translateZ(0) scale(${1 + progress * 0.15}) translateY(${-35 * progress}px)`
      };
    default:
      // 默认轻微放大效果
      return {
        ...baseStyle,
        transform: `translateZ(0) scale(${1 + progress * 0.1})`
      };
  }
});

// 进度百分比
const progressPercentage = computed(() => {
  if (!totalDuration.value) return 0;
  return (currentTime.value / totalDuration.value) * 100;
});

// 字幕字体大小计算
const subtitleFontSize = computed(() => {
  // 获取 preview-container 的宽度
  // 从 containerStyle 计算属性中获取实际的容器宽度
  // containerStyle 返回的是一个对象，其中 width 属性包含了像素值的字符串
  const containerStyleObj = containerStyle.value;
  // 提取像素值（去掉 'px' 后缀）
  const containerWidth = parseInt(containerStyleObj.width);

  // 基础字体大小（像素）
  const baseFontSize = 16;

  // 根据容器宽度计算字体大小
  // 设置最小和最大字体大小限制
  const minFontSize = 24;
  const maxFontSize = 58;

  // 计算字体大小：基础大小 + 容器宽度的比例因子
  // 这里使用容器宽度的 2.5% 作为字体大小
  let fontSize = Math.max(minFontSize, Math.min(maxFontSize, containerWidth * 0.025));

  return `${fontSize}px`;
});

// 监听比例变化
watch(() => props.defaultAspectRatio, (newRatio) => {
  aspectRatio.value = newRatio;
});

// 监听开始索引变化
watch(() => props.startIndex, (newIndex) => {
  if (newIndex !== currentShotIndex.value) {
    currentShotIndex.value = newIndex;
    loadCurrentShot();
  }
});

// 监听自动播放属性
watch(() => props.autoPlay, (shouldAutoPlay) => {
  if (shouldAutoPlay && !isPlaying.value) {
    play();
  } else if (!shouldAutoPlay && isPlaying.value) {
    pause();
  }
});

// 播放/暂停视频
const togglePlayPause = () => {
  if (isPlaying.value) {
    pause();
  } else {
    play();
  }
};

// 使用requestAnimationFrame更新播放进度（包括视频和音频）
const updatePlaybackProgress = () => {
  if (!isPlaying.value) return;

  const previousDuration = calculatePreviousShotsDuration();
  let currentMediaTime = 0;

  // 根据当前播放状态确定时间
  if (currentShot.value?.type === 'video' && videoPlayer.value && !isVideoEnded.value) {
    // 视频正在播放，使用视频时间
    currentMediaTime = videoPlayer.value.currentTime;
  } else if (currentShot.value?.type === 'image' || isVideoEnded.value) {
    // 图片播放或视频已结束但音频仍在播放
    // 计算已经过去的时间
    const shotStartTime = Date.now() - shotStartTimestamp.value;
    currentMediaTime = shotStartTime / 1000; // 转换为秒

    // 确保不超过当前分镜的最大时长
    const maxDuration = getCurrentShotMaxDuration() / 1000;
    if (currentMediaTime > maxDuration) {
      currentMediaTime = maxDuration;
    }
  }

  // 计算总播放时间 = 之前分镜累计时长 + 当前媒体播放时间
  currentTime.value = previousDuration + currentMediaTime;

  // 发送时间更新事件
  emit('time-update', {
    currentTime: currentTime.value,
    duration: totalDuration.value || 0,
    shotIndex: currentShotIndex.value
  });

  // 检查是否需要预加载下一个分镜
  // 获取当前分镜的最大时长（秒）
  const maxDuration = getCurrentShotMaxDuration() / 1000;
  // 计算播放进度百分比
  const playProgress = currentMediaTime / maxDuration;

  // 当播放进度超过70%时，开始预加载下一个分镜
  if (nextShot.value && playProgress > 0.7 && !nextVideoLoaded.value) {
    // 如果下一个分镜是视频，预加载其首帧
    if (nextShot.value.type === 'video') {
      preloadNextVideoFirstFrame();
    }
    // 图片类型会自动通过img标签预加载
  }

  // 继续下一帧动画
  videoAnimationFrameId.value = requestAnimationFrame(updatePlaybackProgress);
};

// 播放
const play = () => {
  if (!currentShot.value) return;

  isPlaying.value = true;
  isVideoEnded.value = false; // 重置视频结束状态
  shotStartTimestamp.value = Date.now(); // 记录开始时间戳
  emit('play-state-change', true);

  if (currentShot.value.type === 'video' && currentShot.value.videoUrl && videoPlayer.value) {
    console.log('准备播放视频分镜:', currentShot.value.videoUrl);

    // 检查是否有缓存的视频资源
    const cachedVideo = videoCache.value[currentShot.value.videoUrl];

    if (cachedVideo && resourceLoadingStatus.value[currentShot.value.videoUrl] === 'loaded') {
      console.log('使用缓存的视频资源:', currentShot.value.videoUrl);

      // 确保视频元素正确设置
      if (videoPlayer.value.src !== cachedVideo.src) {
        videoPlayer.value.src = cachedVideo.src;
        // 等待视频元素准备就绪
        videoPlayer.value.load();
      }
    } else {
      console.log('设置新的视频源:', currentShot.value.videoUrl);
      // 如果没有缓存，直接设置src
      videoPlayer.value.src = currentShot.value.videoUrl;
      videoPlayer.value.load(); // 强制重新加载

      // 同时添加到预加载队列中
      addToPreloadQueue(currentShot.value.videoUrl, 'video', true);
    }

    // 重置视频播放位置
    videoPlayer.value.currentTime = 0;

    // 设置视频音量
    if (typeof currentShot.value.videoVolume === 'number') {
      videoPlayer.value.volume = Math.max(0, Math.min(1, currentShot.value.videoVolume));
      console.log(`设置视频音量: ${videoPlayer.value.volume}`);
    } else {
      videoPlayer.value.volume = 1; // 默认音量为1
    }

    // 等待视频准备就绪后播放
    const playVideo = async () => {
      try {
        // 确保视频已经准备好播放
        if (videoPlayer.value.readyState < 2) {
          console.log('等待视频数据加载...');
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error('视频加载超时'));
            }, 5000);

            const onCanPlay = () => {
              clearTimeout(timeout);
              videoPlayer.value.removeEventListener('canplay', onCanPlay);
              videoPlayer.value.removeEventListener('error', onError);
              resolve();
            };

            const onError = (error) => {
              clearTimeout(timeout);
              videoPlayer.value.removeEventListener('canplay', onCanPlay);
              videoPlayer.value.removeEventListener('error', onError);
              reject(error);
            };

            videoPlayer.value.addEventListener('canplay', onCanPlay);
            videoPlayer.value.addEventListener('error', onError);
          });
        }

        console.log('开始播放视频');

        // 调试：检查视频元素状态
        videoPlaybackDebugger.checkVideoElementState(videoPlayer.value, {
          shotIndex: currentShotIndex.value,
          shotType: currentShot.value.type,
          videoUrl: currentShot.value.videoUrl
        });

        await videoPlayer.value.play();
        console.log('视频播放成功');

        // 播放成功后再次检查状态
        videoPlaybackDebugger.checkVideoElementState(videoPlayer.value, {
          shotIndex: currentShotIndex.value,
          shotType: currentShot.value.type,
          videoUrl: currentShot.value.videoUrl,
          status: 'playing'
        });

      } catch (error) {
        console.error('视频播放失败:', error);
        // 如果视频播放失败，尝试使用图片动画代替
        startImageAnimation();
      }
    };

    // 异步播放视频
    playVideo();

    // 启动统一进度更新动画
    if (videoAnimationFrameId.value) {
      cancelAnimationFrame(videoAnimationFrameId.value);
    }
    videoAnimationFrameId.value = requestAnimationFrame(updatePlaybackProgress);
  } else if (currentShot.value.type === 'image' && currentShot.value.imageUrl) {
    // 开始图片动画
    startImageAnimation();

    // 同时启动统一进度更新
    if (videoAnimationFrameId.value) {
      cancelAnimationFrame(videoAnimationFrameId.value);
    }
    videoAnimationFrameId.value = requestAnimationFrame(updatePlaybackProgress);

    // 如果图片还没有预加载，添加到预加载队列
    if (!imageCache.value[currentShot.value.imageUrl]) {
      addToPreloadQueue(currentShot.value.imageUrl, 'image', true);
    }
  } else {
    // 暂时使用图片动画代替，保证可以继续播放
    startImageAnimation();

    // 同时启动统一进度更新
    if (videoAnimationFrameId.value) {
      cancelAnimationFrame(videoAnimationFrameId.value);
    }
    videoAnimationFrameId.value = requestAnimationFrame(updatePlaybackProgress);
  }

  // 重置当前语音索引
  currentVoiceIndex.value = 0;

  // 播放语音（从第一个开始）
  if (currentShot.value.audios && currentShot.value.audios.length > 0) {
    playAudio(currentShot.value.audios[0].audioUrl);
  }

  // 立即触发一次时间更新，确保音轨音频能够立即检查并播放
  const previousDuration = calculatePreviousShotsDuration();
  const currentMediaTime = 0; // 开始播放时从0开始
  currentTime.value = previousDuration + currentMediaTime;

  // 发送时间更新事件，触发音轨音频检查
  emit('time-update', {
    currentTime: currentTime.value,
    duration: totalDuration.value || 0,
    shotIndex: currentShotIndex.value
  });
};

// 暂停
const pause = () => {
  isPlaying.value = false;
  // 不重置isVideoEnded，因为暂停后恢复播放时需要保持状态
  emit('play-state-change', false);

  // 暂停时退出全屏
  if (isFullscreen.value) {
    if (document.exitFullscreen) {
      document.exitFullscreen().catch(err => {
        console.error('退出全屏失败:', err);
      });
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
  }

  if (videoPlayer.value) {
    videoPlayer.value.pause();
  }

  // 停止统一进度更新动画
  if (videoAnimationFrameId.value) {
    cancelAnimationFrame(videoAnimationFrameId.value);
    videoAnimationFrameId.value = null;
  }

  // 暂停图片动画
  stopImageAnimation();

  // 暂停语音
  if (audioPlayer.value) {
    audioPlayer.value.pause();
  }

  // 发送暂停事件，确保音轨音频也停止播放
  emit('time-update', {
    currentTime: currentTime.value,
    duration: totalDuration.value || 0,
    shotIndex: currentShotIndex.value,
    isPlaying: false // 明确指示已暂停
  });
};

// 视频播放结束事件
const onVideoEnded = () => {
  console.log('视频播放结束');
  // 标记视频已结束
  isVideoEnded.value = true;

  if(currentShot.value?.audios == null || currentShot.value?.audios?.length <= 0){
    playNextShot();
  }

  // // 检查语音是否也播放完成
  // if (!audioPlayer.value || audioPlayer.value.ended) {
  //   console.log('视频和音频都结束了，播放下一个分镜');
  //   // 视频和音频都结束了，播放下一个分镜
  //   playNextShot();
  // } else {
  //   console.log('视频结束但音频仍在播放，继续更新进度');
  //   // 视频结束但音频仍在播放，继续更新进度
  //   // 进度更新会由updatePlaybackProgress函数处理
  // }
};

// 视频时间更新事件 - 保留但不再使用，改为使用requestAnimationFrame更新
const onVideoTimeUpdate = () => {
  // 不再在这里更新进度，进度更新由requestAnimationFrame处理
};

// 当前正在播放的语音索引
const currentVoiceIndex = ref(0);

// 音频播放结束事件
const onAudioEnded = () => {
  console.log('音频播放结束');

  // 清除当前字幕
  currentSubtitle.value = '';

  // 检查是否还有下一个语音需要播放
  if (currentShot.value?.audios &&
    currentShot.value.audios.length > 0 &&
    currentVoiceIndex.value < currentShot.value.audios.length - 1) {

    // 播放下一个语音
    currentVoiceIndex.value++;
    const nextAudioUrl = currentShot.value.audios[currentVoiceIndex.value].audioUrl;
    console.log(`播放下一个语音: ${nextAudioUrl}, 索引: ${currentVoiceIndex.value}`);
    playAudio(nextAudioUrl);
    return;
  }

  // 直接播放下一个分镜
  playNextShot();

  // console.log('所有语音播放完毕，检查视频状态');
  // // 所有语音都播放完毕，检查视频是否也播放完成
  // if (!videoPlayer.value || videoPlayer.value.ended || isVideoEnded.value) {
  //   // 视频和音频都结束了，播放下一个分镜
  //   console.log('视频和音频都结束了，播放下一个分镜');
  //   playNextShot();
  // } else {
  //   console.log('视频仍在播放，等待视频结束');
  // }
};

// 播放下一个分镜
const playNextShot = () => {
  if (currentShotIndex.value < props.shots.length - 1) {
    const fromShot = { ...currentShot.value, index: currentShotIndex.value };
    currentShotIndex.value++;
    const toShot = { ...currentShot.value, index: currentShotIndex.value };

    // 调试日志
    videoPlaybackDebugger.monitorShotTransition(fromShot, toShot);

    loadCurrentShot();
    emit('shot-change', currentShotIndex.value);

    // 发送时间更新事件，确保音轨音频能够立即检查并播放
    const previousDuration = calculatePreviousShotsDuration();
    currentTime.value = previousDuration; // 新分镜的开始时间

    // 发送时间更新事件，触发音轨音频检查
    emit('time-update', {
      currentTime: currentTime.value,
      duration: totalDuration.value || 0,
      shotIndex: currentShotIndex.value,
      isPlaying: isPlaying.value
    });
  } else {
    // 所有分镜播放完成
    isPlaying.value = false;

    // 播放完成时退出全屏
    if (isFullscreen.value) {
      if (document.exitFullscreen) {
        document.exitFullscreen().catch(err => {
          console.error('退出全屏失败:', err);
        });
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }

    emit('playback-completed');
  }
};

// 播放上一个分镜
const playPreviousShot = () => {
  if (currentShotIndex.value > 0) {
    currentShotIndex.value--;
    loadCurrentShot();
    emit('shot-change', currentShotIndex.value);

    // 发送时间更新事件，确保音轨音频能够立即检查并播放
    const previousDuration = calculatePreviousShotsDuration();
    currentTime.value = previousDuration; // 新分镜的开始时间

    // 发送时间更新事件，触发音轨音频检查
    emit('time-update', {
      currentTime: currentTime.value,
      duration: totalDuration.value || 0,
      shotIndex: currentShotIndex.value,
      isPlaying: isPlaying.value
    });
  }
};

// 加载当前分镜
const loadCurrentShot = () => {
  console.log('开始加载分镜:', currentShotIndex.value, currentShot.value);

  // 停止之前的播放
  if (videoPlayer.value) {
    videoPlayer.value.pause();
    // 重置视频元素状态，避免黑屏问题
    videoPlayer.value.currentTime = 0;
    // 清除之前的src，强制重新加载
    if (videoPlayer.value.src) {
      videoPlayer.value.removeAttribute('src');
      videoPlayer.value.load();
    }
  }

  if (audioPlayer.value) {
    audioPlayer.value.pause();
  }

  // 停止统一进度更新动画
  if (videoAnimationFrameId.value) {
    cancelAnimationFrame(videoAnimationFrameId.value);
    videoAnimationFrameId.value = null;
  }

  stopImageAnimation();

  // 重置当前语音索引
  currentVoiceIndex.value = 0;

  // 重置视频结束状态
  isVideoEnded.value = false;

  // 清除字幕
  currentSubtitle.value = '';

  // 重置进度为当前分镜之前的累计时长
  currentTime.value = calculatePreviousShotsDuration();
  imageAnimationProgress.value = 0;

  // 重置开始时间戳
  shotStartTimestamp.value = Date.now();

  // 重置下一个视频加载状态
  nextVideoLoaded.value = false;

  // 如果当前分镜存在，预加载其资源
  if (currentShot.value) {
    console.log('当前分镜类型:', currentShot.value.type, '视频URL:', currentShot.value.videoUrl);

    // 预加载当前分镜资源
    addShotToPreloadQueue(currentShot.value, true);

    // 预加载下一个分镜资源
    if (nextShot.value) {
      addShotToPreloadQueue(nextShot.value, true);
    }

    // 如果是视频分镜，立即开始预加载以避免黑屏
    if (currentShot.value.type === 'video' && currentShot.value.videoUrl) {
      preloadCurrentVideoImmediate();
    }
  }

  // 如果正在播放，则自动播放新的分镜
  if (isPlaying.value) {
    // 使用setTimeout确保DOM已更新和视频预加载完成
    setTimeout(() => {
      play();

      // 发送时间更新事件，确保音轨音频能够立即检查并播放
      emit('time-update', {
        currentTime: currentTime.value,
        duration: totalDuration.value || 0,
        shotIndex: currentShotIndex.value,
        isPlaying: true
      });
    }, 100); // 增加延迟时间，确保视频预加载完成
  } else {
    // 即使不播放，也发送一次时间更新事件，确保UI更新
    emit('time-update', {
      currentTime: currentTime.value,
      duration: totalDuration.value || 0,
      shotIndex: currentShotIndex.value,
      isPlaying: false
    });
  }
};

// 播放音频
const playAudio = (audioSrc) => {
  if (!audioSrc) return;

  console.log(`准备播放音频: ${audioSrc}`);

  // 获取当前播放的音频对象
  if (currentShot.value?.audios && currentShot.value.audios.length > currentVoiceIndex.value) {
    const currentAudio = currentShot.value.audios[currentVoiceIndex.value];
    // 如果存在text字段，则显示为字幕
    if (currentAudio.text) {
      currentSubtitle.value = currentAudio.text;
    } else {
      currentSubtitle.value = '';
    }
  } else {
    currentSubtitle.value = '';
  }

  // 检查是否有缓存的音频资源
  const cachedAudio = audioCache.value[audioSrc];

  // 停止之前的音频播放
  if (audioPlayer.value) {
    audioPlayer.value.pause();
    // 移除之前的事件监听器，避免重复触发
    audioPlayer.value.removeEventListener('ended', onAudioEnded);
  }

  if (cachedAudio) {
    console.log(`使用缓存的音频资源: ${audioSrc}`);

    // 创建新的音频元素，避免使用可能正在被其他地方使用的缓存元素
    audioPlayer.value = new Audio();
    audioPlayer.value.src = cachedAudio.src;
    audioPlayer.value.currentTime = 0;
    audioPlayer.value.addEventListener('ended', onAudioEnded);
  } else {
    console.log(`未找到缓存的音频资源，创建新的: ${audioSrc}`);

    // 创建新的音频元素
    audioPlayer.value = new Audio();
    audioPlayer.value.src = audioSrc;
    audioPlayer.value.addEventListener('ended', onAudioEnded);

    // 同时添加到预加载队列中，但不要重复加载
    addToPreloadQueue(audioSrc, 'audio', true);
  }

  // 设置音频音量
  if (currentShot.value?.audios && currentShot.value.audios.length > currentVoiceIndex.value) {
    const currentAudio = currentShot.value.audios[currentVoiceIndex.value];
    if (typeof currentAudio.volume === 'number') {
      audioPlayer.value.volume = Math.max(0, Math.min(1, currentAudio.volume));
      console.log(`设置音频音量: ${audioPlayer.value.volume}`);
    } else {
      audioPlayer.value.volume = 1; // 默认音量为1
    }
  } else {
    audioPlayer.value.volume = 1; // 默认音量为1
  }

  // 开始播放
  console.log(`开始播放音频: ${audioSrc}`);
  audioPlayer.value.play().catch(error => {
    console.error(`音频播放失败: ${audioSrc}`, error);
    // 尝试继续播放下一个音频或分镜
    onAudioEnded();
  });
};

// 开始图片动画
const startImageAnimation = () => {
  // 清除之前的动画
  stopImageAnimation();

  // 获取当前分镜的时长（毫秒）
  const duration = getCurrentShotMaxDuration();
  const durationInSeconds = duration / 1000;

  // 设置动画更新频率
  const updateInterval = 50; // 毫秒
  const totalSteps = (durationInSeconds * 1000) / updateInterval;
  const progressStep = 1 / totalSteps;

  // 重置进度
  imageAnimationProgress.value = 0;

  // 创建动画定时器 - 仅用于更新图片动画效果，不再负责进度更新
  imageAnimationInterval.value = setInterval(() => {
    if (!isPlaying.value) return;

    // 更新图片动画进度
    imageAnimationProgress.value += progressStep;

    // 检查动画是否完成
    if (imageAnimationProgress.value >= 1) {
      stopImageAnimation();

      // 如果语音也播放完成或没有语音，则播放下一个分镜
      if (!audioPlayer.value || audioPlayer.value.ended) {
        playNextShot();
      }
    }
  }, updateInterval);
};

// 停止图片动画
const stopImageAnimation = () => {
  if (imageAnimationInterval.value) {
    clearInterval(imageAnimationInterval.value);
    imageAnimationInterval.value = null;
  }
};

// 格式化时间，将秒转换为 mm:ss 格式
const formatTime = (seconds) => {
  if (!seconds) return '00:00:00';

  const hours = Math.floor(seconds / 3600);
  seconds %= 3600;
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);

  return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
};

// 获取当前分镜的最大时长（毫秒）
const getCurrentShotMaxDuration = () => {
  if (!currentShot.value) return defaultDuration; // 默认5秒

  // 计算所有语音的总时长
  let countVoiceDuration = 0;
  if (currentShot.value.audios && currentShot.value.audios.length > 0) {
    countVoiceDuration = currentShot.value.audios.reduce((acc, voice) => acc + (voice.audioDuration || 0), 0);
  }else if(currentShot.value?.videoDuration > 0){
    return currentShot.value.videoDuration;
  }

  // 如果视频时长大于0且大于语音总时长，返回视频时长
  // if (currentShot.value.videoDuration > 0 && currentShot.value.videoDuration > countVoiceDuration) {
  //   return currentShot.value.videoDuration;
  // } else 
  if (currentShot.value.playDuration && currentShot.value.playDuration > 0 && currentShot.value.playDuration > countVoiceDuration) {
    return currentShot.value.playDuration;
  }else if (countVoiceDuration > 0) {
    // 如果有语音总时长，返回语音总时长
    return countVoiceDuration;
  } else {
    // 最后使用默认时长
    return defaultDuration; // 默认5秒
  }
};

// 计算从开始到当前分镜之前的所有分镜的累计时长（秒）
const calculatePreviousShotsDuration = () => {
  let totalDuration = 0;

  // 遍历当前分镜之前的所有分镜
  for (let i = 0; i < currentShotIndex.value; i++) {
    const shot = props.shots[i];
    if (!shot) continue;

    // 计算所有语音的总时长
    let countVoiceDuration = 0;
    if (shot.audios && shot.audios.length > 0) {
      countVoiceDuration = shot.audios.reduce((acc, voice) => acc + (voice.audioDuration || 0), 0);
    }else if(shot?.videoDuration > 0){
      countVoiceDuration = shot.videoDuration;
    }

    // 如果视频时长大于0且大于语音总时长，返回视频时长
    let duration;
    // if (shot.videoDuration > 0 && shot.videoDuration > countVoiceDuration) {
    //   duration = shot.videoDuration;
    // } else 
    if (shot.playDuration && shot.playDuration > 0 && shot.playDuration > countVoiceDuration) {
      duration = shot.playDuration;
    }else if (countVoiceDuration > 0) {
      // 如果有语音总时长，返回语音总时长
      duration = countVoiceDuration;
    } else {
      // 最后使用默认时长
      duration = defaultDuration; // 默认5秒
    }

    totalDuration += duration / 1000; // 转换为秒
  }

  return totalDuration;
};

// 跳转到指定分镜
const jumpToShot = (index) => {
  if (index >= 0 && index < props.shots.length) {
    currentShotIndex.value = index;
    loadCurrentShot();
    emit('shot-change', index);
  }
};

// 立即预加载当前视频，避免黑屏
const preloadCurrentVideoImmediate = async () => {
  if (!currentShot.value || currentShot.value.type !== 'video' || !currentShot.value.videoUrl) return;

  const videoUrl = currentShot.value.videoUrl;
  console.log('立即预加载当前视频:', videoUrl);

  try {
    // 检查是否已经在缓存中
    if (videoCache.value[videoUrl] && resourceLoadingStatus.value[videoUrl] === 'loaded') {
      console.log('视频已在缓存中，无需重新加载');
      return;
    }

    // 如果正在加载，等待加载完成
    if (resourceLoadingStatus.value[videoUrl] === 'loading') {
      console.log('视频正在加载中，等待完成');
      return;
    }

    // 开始预加载
    resourceLoadingStatus.value[videoUrl] = 'loading';
    await preloadVideo(videoUrl);
    console.log('当前视频预加载完成:', videoUrl);

  } catch (error) {
    console.error('当前视频预加载失败:', videoUrl, error);
    resourceLoadingStatus.value[videoUrl] = 'error';
  }
};

// 预加载下一个视频的首帧
const preloadNextVideoFirstFrame = () => {
  if (!nextShot.value || nextShot.value.type !== 'video' || !nextVideoPreview.value) return;

  // 如果已经加载过，不再重复加载
  if (nextVideoLoaded.value) return;

  const video = nextVideoPreview.value;

  // 设置视频属性
  video.preload = 'auto';
  video.muted = false;
  video.currentTime = 0;

  // 监听loadeddata事件，表示视频数据已加载
  const onLoaded = () => {
    // 视频加载后暂停在第一帧
    video.pause();
    nextVideoLoaded.value = true;

    // 移除事件监听器
    video.removeEventListener('loadeddata', onLoaded);
  };

  // 添加事件监听器
  video.addEventListener('loadeddata', onLoaded);

  // 开始加载视频
  video.load();
};

// 全屏相关方法
const toggleFullscreen = () => {
  if (!previewWrapper.value) return;

  if (!isFullscreen.value) {
    // 进入全屏
    const element = previewWrapper.value;
    if (element.requestFullscreen) {
      element.requestFullscreen().catch(err => {
        console.error('进入全屏失败:', err);
      });
    } else if (element.webkitRequestFullscreen) {
      element.webkitRequestFullscreen();
    } else if (element.mozRequestFullScreen) {
      element.mozRequestFullScreen();
    } else if (element.msRequestFullscreen) {
      element.msRequestFullscreen();
    }
  } else {
    // 退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen().catch(err => {
        console.error('退出全屏失败:', err);
      });
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
  }
};

// 监听全屏状态变化
const handleFullscreenChange = () => {
  const fullscreenElement = document.fullscreenElement ||
                           document.webkitFullscreenElement ||
                           document.mozFullScreenElement ||
                           document.msFullscreenElement;
  isFullscreen.value = !!fullscreenElement;
};

// 组件挂载后初始化
onMounted(() => {
  // 初始化 ResizeObserver 监听预览容器尺寸变化
  if (previewWrapper.value) {
    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        wrapperSize.value = { width, height };
      }
    });

    resizeObserver.observe(previewWrapper.value);

    // 保存 resizeObserver 实例以便在组件卸载时清理
    currentResizeObserver = resizeObserver;
  }

  // 初始化资源预加载
  if (props.shots && props.shots.length > 0) {
    console.log('开始预加载分镜资源...');
    initPreloadQueue();
  }

  // 如果设置了自动播放，则开始播放
  if (props.autoPlay && props.shots.length > 0) {
    setTimeout(() => {
      play();
    }, 100);
  }

  // 添加全屏事件监听器
  document.addEventListener('fullscreenchange', handleFullscreenChange);
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.addEventListener('mozfullscreenchange', handleFullscreenChange);
  document.addEventListener('MSFullscreenChange', handleFullscreenChange);

  // 初始化时尝试预加载下一个分镜
  if (nextShot.value) {
    // 使用setTimeout确保DOM已更新
    setTimeout(() => {
      if (nextShot.value.type === 'video') {
        preloadNextVideoFirstFrame();
      }
    }, 200);
  }
});

// 组件卸载时清理 ResizeObserver 和音频资源
onUnmounted(() => {
  if (currentResizeObserver) {
    currentResizeObserver.disconnect();
    currentResizeObserver = null;
  }

  // 移除全屏事件监听器
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange);

  // 清理音频资源
  if (audioPlayer.value) {
    audioPlayer.value.pause();
    audioPlayer.value.removeEventListener('ended', onAudioEnded);
    audioPlayer.value = null;
  }

  // 清理动画定时器
  stopImageAnimation();

  // 清理统一进度更新动画帧
  if (videoAnimationFrameId.value) {
    cancelAnimationFrame(videoAnimationFrameId.value);
    videoAnimationFrameId.value = null;
  }

  // 清理视频缓存资源
  Object.values(videoCache.value).forEach(video => {
    if (video && video.src) {
      video.src = '';
      video.load();
    }
  });
  videoCache.value = {};

  // 清理音频缓存资源
  Object.values(audioCache.value).forEach(audio => {
    if (audio && audio.src) {
      audio.src = '';
      audio.load();
    }
  });
  audioCache.value = {};

  // 清理图片缓存资源
  imageCache.value = {};

  // 清理加载状态
  resourceLoadingStatus.value = {};
  preloadQueue.value = [];
  isPreloading.value = false;
  preloadCompleted.value = false;
});

// ResizeObserver 实例引用
let currentResizeObserver = null;

// 暴露给父组件的方法
defineExpose({
  play,
  pause,
  jumpToShot,
  loadCurrentShot,
  playNextShot,
  playPreviousShot,
  toggleFullscreen,
  getCurrentShot: () => currentShot.value,
  getCurrentShotIndex: () => currentShotIndex.value,
  isPlaying: () => isPlaying.value,
  getCurrentTime: () => currentTime.value,
  getTotalDuration: () => totalDuration.value
});
</script>

<style scoped>
.video-editor-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}


.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.42);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
}

.loading-icon {
  font-size: 24px;
  color: #409eff;
  margin-bottom: 8px;
  animation: rotating 2s linear infinite;
}

.loading-text {
  color: #f0f8ffb9;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}

body.dark .loading-overlay {
  background-color: rgba(0, 0, 0, 0.689);
}

body.dark .loading-icon {
  color: var(--primary-color);
}

.circular-icon {
  height: 24px;
  width: 24px;
  animation: rotating 2s linear infinite;
}

.path {
  stroke: #409eff;
  stroke-width: 4;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

body.dark .path {
  stroke: var(--primary-color);
}

.preview-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  overflow: hidden;
  align-items: center;
  justify-content: center;
  padding: 12px;
}

.preview-container {
  position: relative;
  width: calc(100% - 12px);
  border-radius: 2px;
  box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.2);
  background-color: #ffffff7c;
  background-image:
    linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%, #e0e0e0),
    linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%, #e0e0e0);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
  box-sizing: border-box;
  /* margin: 6px; */
  overflow: hidden;
  transition: width 0.2s ease, height 0.2s ease, transform 0.2s ease;
  will-change: width, height, transform;
  backface-visibility: hidden;
}

.video-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  /* background-color: #000000; */
}

.video-player {
  width: 100%;
  height: 100%;
}

.image-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  will-change: transform;
  transform: translateZ(0);
  /* 启用硬件加速 */
}

.preview-image {
  width: 100%;
  height: 100%;
  display: block;
  margin: 0 auto;
  will-change: transform;
  backface-visibility: hidden;
}

/* 下一个分镜预加载样式 */
.next-shot-preview {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0;
  /* transition: opacity 0.5s ease; */
  background-color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.next-video-preview,
.next-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.empty-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  width: 100%;
  height: 100%;
  font-size: 24px;
}

/* Removed unused .el-icon styles */

/* 控制覆盖层 */
.controls-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
}

.controls-overlay:hover {
  opacity: 1;
}

.play-pause-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.play-icon,
.nav-icon {
  font-size: 24px;
  color: white;
}

/* 进度条 */
.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
  display: flex;
  flex-direction: column;
  opacity: 0;
  transition: opacity 0.3s;
  box-sizing: border-box;
}

.preview-container:hover .progress-bar {
  opacity: 1;
}

.progress-track {
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  margin-bottom: 5px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #409eff;
  border-radius: 2px;
}

.time-display {
  color: white;
  font-size: 12px;
  text-align: right;
}

/* 分镜导航 */
.shot-navigation {
  position: absolute;
  bottom: 40px;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  opacity: 0;
  transition: opacity 0.3s;
}

.preview-container:hover .shot-navigation {
  opacity: 1;
}

.nav-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin: 0 10px;
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.shot-indicator {
  color: white;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 5px 10px;
  border-radius: 12px;
  font-size: 12px;
}

/* 暗色模式 */
body.dark .empty-preview {
  color: var(--text-secondary);
}

body.dark .preview-container {
  background-color: var(--bg-secondary, #1e1e2d);
  background-image:
    linear-gradient(45deg, #2a2a3a 25%, transparent 25%, transparent 75%, #2a2a3a 75%, #2a2a3a),
    linear-gradient(45deg, #2a2a3a 25%, transparent 25%, transparent 75%, #2a2a3a 75%, #2a2a3a);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

body.dark .progress-fill {
  background-color: var(--primary-color);
}

/* 字幕样式 */
.subtitle-container {
  position: absolute;
  bottom: 30px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  /* padding: 0 20px; */
  box-sizing: border-box;
  /* z-index: 10; */
}

.subtitle-text {
  display: inline-block;
  /* 移除背景色 */
  /* background-color: rgba(0, 0, 0, 0.509); */
  color: white;
  /* 调整padding，由于没有背景色，可以减小内边距 */
  padding: 10px 16px;
  /* 保留边框圆角，以便未来可能需要添加背景时样式一致 */
  border-radius: 14px;
  /* 字体大小现在通过计算属性动态设置 */
  /* font-size: 2.4vh; */
  max-width: 90%;
  /* 增加最大宽度，确保多行文本有足够空间 */
  line-height: 1.4;
  /* 增加行高，改善多行文本的可读性 */
  transition: all 0.15s ease-out;
  /* 更快的过渡效果 */
  /* border-left: 3px solid rgba(255, 255, 255, 0.6); */
  text-align: center;
  /* 改为居中对齐，使多行文本更美观 */
  /* white-space: nowrap; */
  /* 修改为nowrap，防止文本自动换行 */
  /* overflow: hidden; */
  /* 隐藏溢出部分 */
  /* text-overflow: ellipsis; */
  /* 使用省略号表示溢出的文本 */
  min-height: 1.8em;
  /* 移除阴影 */
  /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); */
  letter-spacing: 0.8px;
  /* word-spacing: 1px; */
  /* 添加文字描边效果 */
  /* text-shadow: 
    -2px -2px 0 #000,  
    2px -2px 0 #000,
    -2px 2px 0 #000,
    2px 2px 0 #000,
    0 5px 8px rgba(0, 0, 0, 0.8); */
  text-shadow:
    -1px -1px 0 #000,
    1px -1px 0 #000,
    -1px 1px 0 #000,
    1px 1px 0 #000;
  /* 增加字体粗细，使文字更清晰 */
  font-weight: 900;
  /* 添加多行文本间距 */
  margin: 0 auto;
}

/* 资源加载状态指示器 */
.resource-loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 20px 30px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
  /* 阻止点击事件穿透 */
}

.preview-container:hover .resource-loading-indicator {
  opacity: 1;
}

.loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>